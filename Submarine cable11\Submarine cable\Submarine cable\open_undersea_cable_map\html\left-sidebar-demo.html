<!DOCTYPE html>
<html>
<head>
    <title>Left Sidebar Cable Search Demo</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />
    <style>
        body { 
            margin: 0; 
            padding: 0; 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        #map { 
            height: 100vh;
            width: calc(100% - 350px);
            margin-left: 350px;
            position: relative;
        }
        
        /* Left Sidebar Styles */
        #sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: 350px;
            height: 100vh;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-right: 1px solid #e0e0e0;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            z-index: 1000;
            overflow-y: auto;
            padding: 20px;
            box-sizing: border-box;
        }
        
        #sidebar h2 {
            margin: 0 0 20px 0;
            color: #2c3e50;
            font-size: 20px;
            font-weight: 600;
        }
        
        .search-section {
            margin-bottom: 25px;
            padding-bottom: 20px;
            border-bottom: 1px solid #e8e8e8;
        }
        
        .search-group {
            margin-bottom: 15px;
            position: relative;
        }
        
        .search-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #555;
            font-size: 14px;
        }
        
        .search-input {
            width: 100%;
            padding: 10px 12px;
            border: 2px solid #e0e0e0;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s ease;
            box-sizing: border-box;
        }
        
        .search-input:focus {
            outline: none;
            border-color: #3498db;
        }
        
        .dropdown-list {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #e0e0e0;
            border-top: none;
            border-radius: 0 0 6px 6px;
            max-height: 200px;
            overflow-y: auto;
            z-index: 1001;
            display: none;
        }
        
        .dropdown-item {
            padding: 10px 12px;
            cursor: pointer;
            border-bottom: 1px solid #f0f0f0;
            font-size: 14px;
        }
        
        .dropdown-item:hover {
            background-color: #f8f9fa;
        }
        
        .button-group {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }
        
        .btn {
            flex: 1;
            padding: 10px 15px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #3498db;
            color: white;
        }
        
        .btn-primary:hover {
            background: #2980b9;
        }
        
        .btn-primary:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
        }
        
        .btn-secondary {
            background: #95a5a6;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #7f8c8d;
        }
        
        .results-section {
            margin-top: 20px;
        }
        
        .results-header {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 16px;
        }
        
        .cable-result {
            background: white;
            border: 1px solid #e8e8e8;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s ease, transform 0.2s ease;
        }
        
        .cable-result:hover {
            border-color: #3498db;
            box-shadow: 0 2px 8px rgba(52, 152, 219, 0.1);
            transform: translateY(-1px);
        }
        
        .cable-name {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
            font-size: 14px;
        }
        
        .cable-details {
            font-size: 12px;
            color: #7f8c8d;
            line-height: 1.4;
        }
        
        .cable-color-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
            vertical-align: middle;
        }
        
        .demo-info {
            background: #e8f4fd;
            border: 1px solid #3498db;
            border-radius: 6px;
            padding: 15px;
            margin-top: 20px;
        }
        
        .demo-info h3 {
            margin: 0 0 10px 0;
            color: #2c3e50;
            font-size: 16px;
        }
        
        .demo-info p {
            margin: 0;
            color: #555;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .highlight-demo {
            margin-top: 15px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
            font-size: 12px;
        }
        
        .highlight-example {
            display: flex;
            align-items: center;
            margin: 5px 0;
        }
        
        .highlight-line {
            width: 30px;
            height: 3px;
            margin-right: 10px;
            border-radius: 2px;
        }
        
        .highlighted {
            background: #e74c3c;
            opacity: 1.0;
        }
        
        .grayed {
            background: #cccccc;
            opacity: 0.1;
        }
    </style>
</head>
<body>
    <!-- Left Sidebar -->
    <div id="sidebar">
        <h2>🌊 Cable Search</h2>
        
        <div class="search-section">
            <div class="search-group">
                <label for="fromCountry">From Country:</label>
                <input type="text" id="fromCountry" class="search-input" placeholder="Select or type country name..." autocomplete="off">
                <div id="fromCountryDropdown" class="dropdown-list"></div>
            </div>
            
            <div class="search-group">
                <label for="toCountry">To Country:</label>
                <input type="text" id="toCountry" class="search-input" placeholder="Select or type country name..." autocomplete="off">
                <div id="toCountryDropdown" class="dropdown-list"></div>
            </div>
            
            <div class="button-group">
                <button id="searchBtn" class="btn btn-primary" disabled>Search Cables</button>
                <button id="clearBtn" class="btn btn-secondary">Clear Search</button>
            </div>
        </div>
        
        <div class="results-section">
            <div id="resultsHeader" class="results-header" style="display: none;">Found Cables:</div>
            <div id="cableResults"></div>
        </div>
        
        <div class="demo-info">
            <h3>🎯 Highlighting Demo</h3>
            <p>This demonstrates the enhanced cable highlighting functionality:</p>
            
            <div class="highlight-demo">
                <div class="highlight-example">
                    <div class="highlight-line highlighted"></div>
                    <span>Matching cables: Full color & opacity</span>
                </div>
                <div class="highlight-example">
                    <div class="highlight-line grayed"></div>
                    <span>Other cables: Barely visible (0.1 opacity)</span>
                </div>
            </div>
            
            <p style="margin-top: 10px; font-size: 12px;">
                When you search for cables between two countries, the matching cables will be highlighted in their original bright colors while all other cables become barely visible (0.1 opacity), making it extremely easy to see exactly where the connections are - just like submarinecablemap.com.
            </p>
        </div>
    </div>
    
    <div id="map"></div>
    <script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
    <script>
        // Initialize the map
        const map = L.map('map').setView([20, 0], 2);
        
        L.tileLayer('https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png', {
            attribution: '© OpenStreetMap contributors © CARTO',
            subdomains: 'abcd',
            maxZoom: 19
        }).addTo(map);

        // Demo countries for testing
        const demoCountries = [
            'South Africa', 'Egypt', 'Morocco', 'Nigeria', 'Kenya', 'Ghana', 'Senegal', 'Tanzania',
            'Angola', 'Mozambique', 'Madagascar', 'Mauritius', 'Seychelles', 'Djibouti', 'Somalia',
            'United Kingdom', 'France', 'Spain', 'Italy', 'Germany', 'Netherlands', 'Portugal',
            'Greece', 'Norway', 'Denmark', 'Sweden', 'Finland', 'Ireland', 'Belgium', 'Malta',
            'Cyprus', 'Bulgaria', 'Romania', 'Croatia', 'Albania', 'Turkey', 'Russia'
        ].sort();

        // Add some demo cable lines to show highlighting effect
        const demoCables = [
            { name: '2Africa', color: '#e74c3c', coords: [[35, -5], [40, 15], [30, 25]] },
            { name: 'WACS', color: '#3498db', coords: [[0, -10], [10, 0], [20, 10]] },
            { name: 'ACE', color: '#f39c12', coords: [[-10, -15], [0, -5], [15, 5]] },
            { name: 'SAT-3', color: '#9b59b6', coords: [[-20, -10], [-10, 0], [5, 15]] }
        ];

        const cableLayer = L.layerGroup().addTo(map);
        
        // Add demo cables to map
        demoCables.forEach(cable => {
            const line = L.polyline(cable.coords, {
                color: cable.color,
                weight: 3,
                opacity: 0.8
            }).addTo(cableLayer);
            
            line.bindPopup(`<b>${cable.name}</b><br>Demo submarine cable`);
        });

        // Setup demo functionality
        setupDemoSearch();

        function setupDemoSearch() {
            const fromCountryInput = document.getElementById('fromCountry');
            const toCountryInput = document.getElementById('toCountry');
            const fromDropdown = document.getElementById('fromCountryDropdown');
            const toDropdown = document.getElementById('toCountryDropdown');
            const searchBtn = document.getElementById('searchBtn');
            const clearBtn = document.getElementById('clearBtn');

            // Setup autocomplete for both inputs
            setupAutocomplete(fromCountryInput, fromDropdown, demoCountries);
            setupAutocomplete(toCountryInput, toDropdown, demoCountries);

            // Enable search button when both countries are selected
            function updateSearchButton() {
                const fromValid = demoCountries.includes(fromCountryInput.value);
                const toValid = demoCountries.includes(toCountryInput.value);
                searchBtn.disabled = !(fromValid && toValid && fromCountryInput.value !== toCountryInput.value);
            }

            fromCountryInput.addEventListener('input', updateSearchButton);
            toCountryInput.addEventListener('input', updateSearchButton);

            // Demo search functionality
            searchBtn.addEventListener('click', performDemoSearch);
            clearBtn.addEventListener('click', clearDemoSearch);
        }

        function setupAutocomplete(input, dropdown, countries) {
            input.addEventListener('input', function() {
                const value = this.value.toLowerCase();
                dropdown.innerHTML = '';
                
                if (value.length === 0) {
                    dropdown.style.display = 'none';
                    return;
                }

                const filtered = countries.filter(country => 
                    country.toLowerCase().includes(value)
                ).slice(0, 10);

                if (filtered.length > 0) {
                    filtered.forEach(country => {
                        const item = document.createElement('div');
                        item.className = 'dropdown-item';
                        item.textContent = country;
                        item.addEventListener('click', function() {
                            input.value = country;
                            dropdown.style.display = 'none';
                            input.dispatchEvent(new Event('input'));
                        });
                        dropdown.appendChild(item);
                    });
                    dropdown.style.display = 'block';
                } else {
                    dropdown.style.display = 'none';
                }
            });

            // Hide dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!input.contains(e.target) && !dropdown.contains(e.target)) {
                    dropdown.style.display = 'none';
                }
            });
        }

        function performDemoSearch() {
            const fromCountry = document.getElementById('fromCountry').value;
            const toCountry = document.getElementById('toCountry').value;
            
            console.log(`🔍 Demo search: ${fromCountry} to ${toCountry}`);
            
            // Simulate highlighting: highlight first 2 cables, gray out others
            const highlightedCables = demoCables.slice(0, 2);
            const grayedCables = demoCables.slice(2);
            
            // Apply highlighting effect (like submarinecablemap.com)
            cableLayer.eachLayer((layer, index) => {
                if (index < 2) {
                    // Highlight matching cables
                    layer.setStyle({
                        color: demoCables[index].color,
                        opacity: 1.0,
                        weight: 4
                    });
                } else {
                    // Make other cables barely visible
                    layer.setStyle({
                        color: '#cccccc',
                        opacity: 0.1, // Barely visible like submarinecablemap.com
                        weight: 1
                    });
                }
            });
            
            // Show demo results
            displayDemoResults(highlightedCables, fromCountry, toCountry);
        }

        function clearDemoSearch() {
            console.log('🧹 Clearing demo search');
            
            document.getElementById('fromCountry').value = '';
            document.getElementById('toCountry').value = '';
            document.getElementById('searchBtn').disabled = true;
            document.getElementById('resultsHeader').style.display = 'none';
            document.getElementById('cableResults').innerHTML = '';
            
            // Restore original cable appearance
            cableLayer.eachLayer((layer, index) => {
                layer.setStyle({
                    color: demoCables[index].color,
                    opacity: 0.8,
                    weight: 3
                });
            });
        }

        function displayDemoResults(results, fromCountry, toCountry) {
            const resultsHeader = document.getElementById('resultsHeader');
            const cableResults = document.getElementById('cableResults');
            
            resultsHeader.style.display = 'block';
            resultsHeader.textContent = `Found ${results.length} cable${results.length > 1 ? 's' : ''} (Demo):`;
            
            cableResults.innerHTML = '';
            
            results.forEach(cable => {
                const cableDiv = document.createElement('div');
                cableDiv.className = 'cable-result';
                
                cableDiv.innerHTML = `
                    <div class="cable-name">
                        <span class="cable-color-indicator" style="background-color: ${cable.color}"></span>
                        ${cable.name}
                    </div>
                    <div class="cable-details">
                        Demo cable connecting ${fromCountry} and ${toCountry}<br>
                        Status: Highlighted on map
                    </div>
                `;
                
                cableDiv.addEventListener('click', () => {
                    alert(`Clicked on ${cable.name}\n\nIn the full version:\n• Map centers on this cable\n• Cable gets temporary 8px highlighting\n• Popup shows detailed information\n• 4-second highlight duration`);
                });
                
                cableResults.appendChild(cableDiv);
            });
        }
    </script>
</body>
</html>
