# Submarine Cable Map - Sidebar Search Feature

## Overview

The submarine cable map now includes a comprehensive sidebar search feature that allows users to find and visualize submarine cables connecting African and European countries. This feature provides an intuitive interface similar to submarinecablemap.com with enhanced filtering and visualization capabilities.

## Features

### 🔍 **Smart Country Search**
- **Dual Dropdown Search**: Two autocomplete-enabled search fields for "From Country" and "To Country"
- **Dynamic Country Population**: Automatically scans cable and landing point data to populate available countries
- **Africa/Europe Focus**: Only includes countries from Africa and Europe that have submarine cable landing points
- **Real-time Validation**: Search button is only enabled when both valid countries are selected

### 🎨 **Visual Cable Filtering**
- **Highlight Matching Cables**: Found cables are displayed with full opacity and original colors
- **Dim Non-matching Cables**: Other cables are dimmed to ~20% opacity with gray/muted colors
- **Preserve All Cables**: All cables remain visible but de-emphasized for context
- **Clear Search Function**: One-click restoration to normal view

### 📋 **Interactive Results Display**
- **Scrollable Cable List**: Shows all found cables with detailed information
- **Cable Information**: Displays cable name, length, RFS year, and owners
- **Color-coded Indicators**: Each cable result shows its map color for easy identification
- **Clickable Results**: Click any cable to center the map and highlight it temporarily

### 🗺️ **Map Integration**
- **Auto-centering**: Clicking a cable result centers the map on that specific cable
- **Temporary Highlighting**: Selected cables are temporarily highlighted for 3 seconds
- **Popup Information**: Shows detailed cable information in map popups
- **Responsive Design**: Adapts to different screen sizes including mobile devices

## Technical Implementation

### Data Sources
- **Cable Data**: `cable-geo.json` - Contains cable geometry and basic properties
- **Landing Point Data**: `landing-point-geo.json` - Contains landing point locations and countries
- **Individual Cable Files**: `cable/{cable-id}.json` - Contains detailed cable information including landing points

### Country Extraction Algorithm
The system dynamically determines available countries by:
1. Scanning all landing points in the dataset
2. Extracting country names from landing point locations
3. Filtering to include only African and European countries
4. Cross-referencing with known submarine cable countries

### Search Algorithm
1. **Input Validation**: Ensures both countries are valid and different
2. **Cable Matching**: Loads individual cable data files to find connections
3. **Asynchronous Processing**: Uses Promise.all for efficient parallel loading
4. **Error Handling**: Gracefully handles missing or invalid cable data

### Visual Updates
- **Style Preservation**: Stores original cable styles before applying filters
- **Dynamic Styling**: Updates cable opacity, color, and weight based on search results
- **Restoration**: Restores original styles when search is cleared

## Usage Instructions

### Basic Search
1. **Select From Country**: Type or select a country from the dropdown
2. **Select To Country**: Type or select a different country from the dropdown
3. **Click Search**: The "Search Cables" button becomes enabled when both fields are valid
4. **View Results**: Found cables appear in the sidebar and are highlighted on the map

### Interacting with Results
- **Click Cable Names**: Center the map on specific cables
- **Hover Over Cables**: See detailed information in map popups
- **Clear Search**: Reset the view to show all cables normally

### Mobile Support
- **Responsive Layout**: Sidebar adapts to smaller screens
- **Touch-friendly**: All interactive elements are optimized for touch devices
- **Collapsible Design**: On very small screens, sidebar becomes a top panel

## File Structure

```
html/
├── index.html              # Main map with sidebar functionality
├── sidebar-demo.html       # Standalone demo of sidebar features
└── SIDEBAR_README.md       # This documentation file

cable/
├── cable-geo.json          # Main cable geometry data
├── {cable-id}.json         # Individual cable detail files
└── ...

landing-point/
├── landing-point-geo.json  # Landing point locations and countries
└── ...
```

## Styling and Design

### Color Scheme
- **Primary Blue**: #3498db (search buttons, highlights)
- **Secondary Gray**: #95a5a6 (clear button, muted text)
- **Background**: rgba(255, 255, 255, 0.95) with backdrop blur
- **Borders**: #e0e0e0 (subtle borders throughout)

### Typography
- **Font Family**: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif
- **Hierarchy**: Clear font weights and sizes for different content types
- **Readability**: High contrast and appropriate line heights

### Layout
- **Fixed Sidebar**: 350px width on desktop, responsive on mobile
- **Map Adjustment**: Map automatically adjusts to accommodate sidebar
- **Scrollable Content**: Results section scrolls independently
- **Visual Hierarchy**: Clear separation between search and results sections

## Browser Compatibility

- **Modern Browsers**: Chrome 60+, Firefox 55+, Safari 12+, Edge 79+
- **Mobile Browsers**: iOS Safari 12+, Chrome Mobile 60+
- **Required Features**: ES6 Promises, Fetch API, CSS Grid/Flexbox

## Performance Considerations

- **Lazy Loading**: Cable details are loaded only when needed
- **Efficient Filtering**: Uses Set operations for fast country lookups
- **Debounced Search**: Autocomplete includes built-in debouncing
- **Memory Management**: Properly cleans up event listeners and references

## Future Enhancements

### Potential Improvements
- **Advanced Filters**: Filter by cable capacity, technology, or year
- **Route Visualization**: Show specific cable routes between countries
- **Export Functionality**: Export search results to CSV or JSON
- **Bookmarkable Searches**: URL parameters for sharing specific searches
- **Multi-country Search**: Support for searching cables connecting multiple countries

### API Integration
- **Real-time Data**: Connect to live submarine cable databases
- **Status Updates**: Show cable operational status and maintenance schedules
- **Performance Metrics**: Display cable capacity and utilization data

## Troubleshooting

### Common Issues
1. **No Countries in Dropdown**: Check that cable and landing point data files are accessible
2. **Search Not Working**: Verify that individual cable JSON files are available
3. **Map Not Centering**: Ensure cable geometry data is valid
4. **Styling Issues**: Check that CSS is loading properly and browser supports modern features

### Debug Information
- Open browser console to see detailed logging
- Check network tab for failed file requests
- Verify that all required data files are present and accessible

## Credits

This sidebar feature was designed to enhance the submarine cable map with user-friendly search capabilities while maintaining the professional appearance and functionality of the original map interface.
