<!DOCTYPE html>
<html>
<head>
    <title>Cable Visibility Test - Like SubmarineCableMap.com</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />
    <style>
        body { 
            margin: 0; 
            padding: 0; 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        #map { 
            height: 100vh;
            width: calc(100% - 350px);
            margin-left: 350px;
        }
        
        #sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: 350px;
            height: 100vh;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-right: 1px solid #e0e0e0;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            z-index: 1000;
            overflow-y: auto;
            padding: 20px;
            box-sizing: border-box;
        }
        
        .test-section {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
            border-left: 4px solid #3498db;
        }
        
        .test-button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px 5px 5px 0;
            font-size: 14px;
        }
        
        .test-button:hover {
            background: #2980b9;
        }
        
        .visibility-demo {
            margin: 15px 0;
        }
        
        .cable-line {
            height: 4px;
            margin: 8px 0;
            border-radius: 2px;
            position: relative;
        }
        
        .highlighted {
            background: #e74c3c;
            opacity: 1.0;
        }
        
        .barely-visible {
            background: #cccccc;
            opacity: 0.1;
        }
        
        .label {
            font-size: 12px;
            margin-left: 10px;
            vertical-align: top;
            color: #555;
        }
        
        .stats {
            background: #e8f5e8;
            padding: 10px;
            border-radius: 4px;
            margin-top: 15px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div id="sidebar">
        <h2>🔍 Cable Visibility Test</h2>
        
        <div class="test-section">
            <h3>Visibility Comparison</h3>
            <p>This demonstrates the extreme visibility contrast like submarinecablemap.com:</p>
            
            <div class="visibility-demo">
                <div style="display: flex; align-items: center;">
                    <div class="cable-line highlighted" style="width: 200px;"></div>
                    <span class="label">Highlighted: Opacity 1.0 (Full visibility)</span>
                </div>
                
                <div style="display: flex; align-items: center;">
                    <div class="cable-line barely-visible" style="width: 200px;"></div>
                    <span class="label">Others: Opacity 0.1 (Barely visible)</span>
                </div>
            </div>
            
            <div class="stats">
                <strong>Contrast Ratio:</strong> 10:1 (Highlighted cables are 10x more visible)<br>
                <strong>Effect:</strong> Only search results are clearly visible, others fade into background
            </div>
        </div>
        
        <div class="test-section">
            <h3>Test Controls</h3>
            <button class="test-button" onclick="highlightCables()">Highlight Sample Cables</button>
            <button class="test-button" onclick="resetView()">Reset to Normal</button>
            <button class="test-button" onclick="showStats()">Show Statistics</button>
        </div>
        
        <div class="test-section">
            <h3>Expected Behavior</h3>
            <ul style="font-size: 14px; line-height: 1.6;">
                <li><strong>Search Results:</strong> Bright colors, full opacity (1.0)</li>
                <li><strong>Other Cables:</strong> Light gray (#cccccc), barely visible (0.1 opacity)</li>
                <li><strong>Visual Effect:</strong> Only relevant cables stand out clearly</li>
                <li><strong>User Experience:</strong> Easy identification of connections</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h3>🎯 Test Instructions</h3>
            <ol style="font-size: 14px; line-height: 1.6;">
                <li>Click "Highlight Sample Cables" to see the effect</li>
                <li>Notice how some cables become barely visible</li>
                <li>Only highlighted cables should be clearly visible</li>
                <li>This matches submarinecablemap.com behavior</li>
            </ol>
        </div>
        
        <div id="testResults" class="test-section" style="display: none;">
            <h3>📊 Test Results</h3>
            <div id="statsOutput"></div>
        </div>
    </div>
    
    <div id="map"></div>
    
    <script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
    <script>
        // Initialize the map
        const map = L.map('map').setView([20, 0], 2);
        
        L.tileLayer('https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png', {
            attribution: '© OpenStreetMap contributors © CARTO',
            subdomains: 'abcd',
            maxZoom: 19
        }).addTo(map);

        // Create test cables with different colors
        const testCables = [
            { name: '2Africa', color: '#e74c3c', coords: [[35, -5], [40, 15], [30, 25]], highlighted: true },
            { name: 'WACS', color: '#3498db', coords: [[0, -10], [10, 0], [20, 10]], highlighted: true },
            { name: 'ACE', color: '#f39c12', coords: [[-10, -15], [0, -5], [15, 5]], highlighted: false },
            { name: 'SAT-3', color: '#9b59b6', coords: [[-20, -10], [-10, 0], [5, 15]], highlighted: false },
            { name: 'MainOne', color: '#27ae60', coords: [[-5, -20], [5, -10], [15, 0]], highlighted: false },
            { name: 'GLO-1', color: '#e67e22', coords: [[10, -25], [20, -15], [25, -5]], highlighted: false }
        ];

        const cableLayer = L.layerGroup().addTo(map);
        const cableLayers = [];
        
        // Add test cables to map
        testCables.forEach((cable, index) => {
            const line = L.polyline(cable.coords, {
                color: cable.color,
                weight: 3,
                opacity: 0.8
            }).addTo(cableLayer);
            
            line.bindPopup(`<b>${cable.name}</b><br>Test submarine cable`);
            cableLayers.push(line);
        });

        function highlightCables() {
            console.log('🎯 Applying extreme visibility contrast (like submarinecablemap.com)');
            
            let highlightedCount = 0;
            let dimmedCount = 0;
            
            testCables.forEach((cable, index) => {
                const layer = cableLayers[index];
                
                if (cable.highlighted) {
                    // HIGHLIGHTED: Full visibility
                    layer.setStyle({
                        color: cable.color,
                        opacity: 1.0,  // Full opacity
                        weight: 4
                    });
                    highlightedCount++;
                    console.log(`✅ Highlighted: ${cable.name} (opacity 1.0)`);
                } else {
                    // BARELY VISIBLE: Like submarinecablemap.com
                    layer.setStyle({
                        color: '#cccccc',  // Light gray
                        opacity: 0.1,     // Barely visible
                        weight: 1
                    });
                    dimmedCount++;
                    console.log(`👻 Barely visible: ${cable.name} (opacity 0.1)`);
                }
            });
            
            showTestResults(highlightedCount, dimmedCount);
        }

        function resetView() {
            console.log('🔄 Resetting to normal view');
            
            testCables.forEach((cable, index) => {
                const layer = cableLayers[index];
                layer.setStyle({
                    color: cable.color,
                    opacity: 0.8,
                    weight: 3
                });
            });
            
            document.getElementById('testResults').style.display = 'none';
        }

        function showStats() {
            const highlighted = testCables.filter(c => c.highlighted).length;
            const total = testCables.length;
            const dimmed = total - highlighted;
            
            alert(`Cable Statistics:\n\n` +
                  `Total cables: ${total}\n` +
                  `Highlighted: ${highlighted} (opacity 1.0)\n` +
                  `Barely visible: ${dimmed} (opacity 0.1)\n\n` +
                  `Contrast ratio: 10:1\n` +
                  `Effect: Only highlighted cables are clearly visible`);
        }

        function showTestResults(highlighted, dimmed) {
            const testResults = document.getElementById('testResults');
            const statsOutput = document.getElementById('statsOutput');
            
            testResults.style.display = 'block';
            
            statsOutput.innerHTML = `
                <div style="font-size: 14px; line-height: 1.6;">
                    <strong>✅ Test Applied Successfully</strong><br><br>
                    
                    <strong>Highlighted Cables:</strong> ${highlighted}<br>
                    • Color: Original bright colors<br>
                    • Opacity: 1.0 (fully visible)<br>
                    • Weight: 4px<br><br>
                    
                    <strong>Dimmed Cables:</strong> ${dimmed}<br>
                    • Color: #cccccc (light gray)<br>
                    • Opacity: 0.1 (barely visible)<br>
                    • Weight: 1px<br><br>
                    
                    <strong>Visibility Contrast:</strong> 10:1 ratio<br>
                    <strong>Result:</strong> Only search results are clearly visible
                </div>
            `;
        }

        // Auto-run the highlight test after 2 seconds
        setTimeout(() => {
            console.log('🚀 Auto-running visibility test...');
            highlightCables();
        }, 2000);
    </script>
</body>
</html>
